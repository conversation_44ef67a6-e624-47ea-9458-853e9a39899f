/**
 * handleSession函数 - 真正一步到位链式版本
 * 使用立即执行函数实现 new TransformStream({...}).readable.pipeTo(...)
 * 完全无中间变量的链式调用
 */
export async function handleSession(request, env, ctx, protocolMode) {
    const { 0: client, 1: server } = Object.values(new WebSocketPair());
    server.accept();

    const earlyHeader = request.headers.get("sec-websocket-protocol") || "";
    
    // 日志系统
    let address = '';
    let portWithRandomLog = '';
    const log = (info, event = '') => {
        console.log(`[${address}:${portWithRandomLog}] ${info}`, event || '');
    };

    // 状态变量
    let headerDone = false;
    let tcpInterface = null;
    let tcpWriter = null;
    let tcpReader = null;

    /* ========== 真正一步到位链式调用 ========== */
    // 使用立即执行函数实现完全链式调用
    ((transformStream) => {
        const holdWriter = transformStream.writable.getWriter();
        
        // 处理早期数据
        if (earlyHeader) {
            try {
                holdWriter.write(decodeBase64Url(earlyHeader)).catch(() => { });
            } catch (e) { }
        }

        // WebSocket事件处理
        server.addEventListener("message", (e) => {
            try { holdWriter.write(e.data); } catch {}
        });
        
        server.addEventListener("close", () => {
            try { holdWriter.close(); } catch {}
        });
        
        server.addEventListener("error", (event) => {
            try { holdWriter.abort(event); } catch {}
        });

        // 返回readable用于链式调用
        return transformStream.readable;
    })(
        // 传入新创建的TransformStream
        new TransformStream({
            transform(chunk, controller) {
                controller.enqueue(chunk);
            }
        })
    )
    .pipeTo(
        new WritableStream({
            async write(chunk) {
                try {
                    /* ---------- 首包：解析协议 + 建TCP ---------- */
                    if (!headerDone) {
                        headerDone = true;
                        log('开始处理协议头');

                        // 解析协议头
                        const header = await parseProtocolHeader(chunk, server, protocolMode);
                        address = header.addressRemote;
                        portWithRandomLog = `${header.portRemote}_${Math.random().toString(16).slice(2, 8)}`;
                        log(`协议头解析成功: ${address}:${header.portRemote}`);

                        // 建立TCP连接（含重试）
                        try {
                            tcpInterface = await dial(header, globalControllerConfig.connectMode, protocolMode);
                            await tcpInterface.opened;
                            log(`首次连接成功`);
                        } catch (connectError) {
                            log(`首次连接失败: ${connectError.message}`);
                            tcpInterface = await dial(header, globalControllerConfig.retryMode, protocolMode);
                            await tcpInterface.opened;
                            log(`重试连接成功`);
                        }

                        // 准备reader/writer
                        tcpWriter = tcpInterface.writable.getWriter();
                        tcpReader = tcpInterface.readable.getReader();

                        // 发送初始数据
                        if (header.rawClientData?.byteLength > 0) {
                            await tcpWriter.write(header.rawClientData);
                            log(`发送初始数据: ${header.rawClientData.byteLength} bytes`);
                        }

                        // 启动下游数据传输：TCP → WebSocket
                        (async () => {
                            try {
                                while (true) {
                                    const { value, done } = await tcpReader.read();
                                    if (done) break;
                                    if (value?.byteLength > 0) {
                                        server.send(value);
                                        log(`发送WebSocket数据: ${value.byteLength} bytes`);
                                    }
                                }
                            } catch (error) {
                                log(`下游传输错误: ${error.message}`);
                            } finally {
                                try { tcpReader?.releaseLock(); } catch {}
                            }
                        })();

                        return;
                    }

                    /* ---------- 后续数据：直接写TCP ---------- */
                    if (tcpWriter) {
                        await tcpWriter.write(chunk);
                        log(`写入TCP数据: ${chunk.byteLength} bytes`);
                    } else {
                        throw new Error("TCP not ready");
                    }
                    
                } catch (error) {
                    log(`处理错误: ${error.message}`);
                    throw error;
                }
            },

            close() {
                log('链式处理完成');
                try { tcpWriter?.close(); } catch {}
            },

            abort(e) {
                log('链式处理中止', e);
                try { tcpWriter?.abort(e); } catch {}
            }
        })
    )
    .catch(e => {
        log(`链式管道错误: ${e.message}`);
        try { server.close(1013, e.message); } catch {}
    });

    return new Response(null, { status: 101, webSocket: client });
}

// 需要原_worker.js中的以下函数：
// - parseProtocolHeader
// - dial  
// - decodeBase64Url
// - globalControllerConfig
