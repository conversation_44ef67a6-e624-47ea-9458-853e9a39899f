/**
 * handleSession函数 - 完全链式调用版本
 * 使用 new TransformStream({...}).readable.pipeTo(...) 模式
 * 完全的链式调用，不保存中间变量
 */
export async function handleSession(request, env, ctx, protocolMode) {
    const { 0: client, 1: server } = Object.values(new WebSocketPair());
    server.accept();

    const earlyHeader = request.headers.get("sec-websocket-protocol") || "";
    let tcpInterface = null;

    // 日志系统
    let address = '';
    let portWithRandomLog = '';
    const log = (info, event = '') => {
        console.log(`[${address}:${portWithRandomLog}] ${info}`, event || '');
    };

    // 状态变量
    let headerDone = false;
    let tcpWriter = null;
    let tcpReader = null;

    /* ========== 完全链式调用：new TransformStream().readable.pipeTo() ========== */

    // 创建TransformStream并立即获取writer
    const { readable, writable } = new TransformStream({
        transform(chunk, controller) {
            controller.enqueue(chunk);
        }
    });

    const holdWriter = writable.getWriter();

    // 处理早期数据
    if (earlyHeader) {
        try {
            holdWriter.write(decodeBase64Url(earlyHeader)).catch(() => { });
        } catch (e) { }
    }

    // WebSocket事件处理 - 数据写入TransformStream
    const handleMessage = (e) => {
        try {
            holdWriter.write(e.data);
        } catch (e) {
            // 忽略写入错误
        }
    };

    const handleClose = () => {
        try {
            holdWriter.close();
        } catch (e) {
            // 忽略关闭错误
        }
    };

    const handleError = (event) => {
        try {
            holdWriter.abort(event);
        } catch (e) {
            // 忽略错误传播问题
        }
    };

    server.addEventListener("message", handleMessage);
    server.addEventListener("close", handleClose);
    server.addEventListener("error", handleError);

    // 完全链式调用：直接使用readable.pipeTo()
    readable.pipeTo(
        new WritableStream({
            async write(chunk) {
                try {
                    /* ---------- 首包：解析协议 + 建TCP ---------- */
                    if (!headerDone) {
                        headerDone = true;
                        log('开始处理协议头');

                        // 1) 解析协议头
                        const header = await parseProtocolHeader(chunk, server, protocolMode);
                        address = header.addressRemote;
                        portWithRandomLog = `${header.portRemote}_${Math.random().toString(16).slice(2, 8)}`;
                        log(`协议头解析成功: ${address}:${header.portRemote}`);

                        // 2) 建立TCP连接（含重试）
                        try {
                            tcpInterface = await dial(header, globalControllerConfig.connectMode, protocolMode);
                            await tcpInterface.opened;
                            log(`首次连接成功: ${globalControllerConfig.connectMode}`);
                        } catch (connectError) {
                            log(`首次连接失败: ${connectError.message}`);
                            try {
                                tcpInterface = await dial(header, globalControllerConfig.retryMode, protocolMode);
                                await tcpInterface.opened;
                                log(`重试连接成功: ${globalControllerConfig.retryMode}`);
                            } catch (retryError) {
                                log(`重试连接失败: ${retryError.message}`);
                                throw retryError;
                            }
                        }

                        // 3) 准备reader/writer
                        tcpWriter = tcpInterface.writable.getWriter();
                        tcpReader = tcpInterface.readable.getReader();

                        // 4) 发送初始数据
                        if (header.rawClientData && header.rawClientData.byteLength > 0) {
                            await tcpWriter.write(header.rawClientData);
                            log(`发送初始数据: ${header.rawClientData.byteLength} bytes`);
                        }

                        // 5) 启动下游数据传输：TCP → WebSocket
                        (async () => {
                            try {
                                while (true) {
                                    const { value: tcpChunk, done } = await tcpReader.read();
                                    if (done) break;
                                    if (tcpChunk && tcpChunk.byteLength > 0) {
                                        server.send(tcpChunk);
                                        log(`发送WebSocket数据: ${tcpChunk.byteLength} bytes`);
                                    }
                                }
                            } catch (error) {
                                log(`下游传输错误: ${error.message}`);
                            } finally {
                                try { tcpReader?.releaseLock(); } catch {}
                            }
                        })();

                        return; // 首包处理完成
                    }

                    /* ---------- 后续数据：直接写TCP ---------- */
                    if (tcpWriter) {
                        await tcpWriter.write(chunk);
                        log(`写入TCP数据: ${chunk.byteLength} bytes`);
                    } else {
                        log('TCP连接未就绪');
                        throw new Error("TCP not ready");
                    }
                    
                } catch (error) {
                    log(`WritableStream处理错误: ${error.message}`);
                    throw error;
                }
            },

            close() {
                log('WritableStream关闭');
                try { 
                    tcpWriter?.close(); 
                } catch {}
            },

            abort(e) {
                log('WritableStream中止', e);
                try { 
                    tcpWriter?.abort(e); 
                } catch {}
            },
        })
    )
    .catch(e => {
        // 整条管道出错 → 关闭WebSocket
        log(`管道错误: ${e.message}`);
        try { 
            server.close(1013, e.message); 
        } catch {}
    });

    return new Response(null, { status: 101, webSocket: client });
}

// 需要原_worker.js中的以下函数：
// - parseProtocolHeader
// - dial
// - decodeBase64Url
// - globalControllerConfig
