/**
 * TransformStream实用性链式应用示例集合
 * 基于handleSession_TransformStream_Chain.js的成功模式
 */

// ========== 示例1: 数据压缩传输链 ==========
export function createCompressionChain() {
    const compressionHold = new TransformStream({
        transform(chunk, controller) {
            // 模拟数据压缩
            const compressed = new TextEncoder().encode(
                JSON.stringify({ 
                    data: Array.from(new Uint8Array(chunk)).join(','),
                    compressed: true,
                    timestamp: Date.now()
                })
            );
            controller.enqueue(compressed);
        }
    });

    // 直接链式处理
    compressionHold.readable.pipeTo(
        new WritableStream({
            write(chunk) {
                const data = JSON.parse(new TextDecoder().decode(chunk));
                console.log(`压缩数据传输: ${data.data.length} bytes, 时间: ${data.timestamp}`);
            }
        })
    );

    return compressionHold.writable.getWriter();
}

// ========== 示例2: 实时日志处理链 ==========
export function createLogProcessingChain() {
    const logHold = new TransformStream({
        transform(chunk, controller) {
            controller.enqueue(chunk);
        }
    });

    let logBuffer = [];
    
    logHold.readable.pipeTo(
        new WritableStream({
            async write(chunk) {
                const logEntry = new TextDecoder().decode(chunk);
                
                // 解析日志级别
                const level = logEntry.includes('[ERROR]') ? 'ERROR' : 
                             logEntry.includes('[WARN]') ? 'WARN' : 'INFO';
                
                const processedLog = {
                    level,
                    message: logEntry,
                    timestamp: new Date().toISOString(),
                    id: Math.random().toString(36).slice(2)
                };

                logBuffer.push(processedLog);
                
                // 批量处理
                if (logBuffer.length >= 5) {
                    console.log('批量日志处理:', logBuffer);
                    logBuffer = [];
                }

                // 错误日志立即处理
                if (level === 'ERROR') {
                    console.error('🚨 紧急错误:', processedLog);
                }
            }
        })
    );

    return logHold.writable.getWriter();
}

// ========== 示例3: WebSocket消息路由链 ==========
export function createMessageRoutingChain(routes) {
    const routingHold = new TransformStream({
        transform(chunk, controller) {
            controller.enqueue(chunk);
        }
    });

    routingHold.readable.pipeTo(
        new WritableStream({
            async write(chunk) {
                try {
                    const message = JSON.parse(new TextDecoder().decode(chunk));
                    const { type, data, target } = message;

                    console.log(`路由消息: ${type} -> ${target}`);

                    // 根据消息类型路由
                    switch (type) {
                        case 'broadcast':
                            routes.broadcast?.(data);
                            break;
                        case 'private':
                            routes.private?.(target, data);
                            break;
                        case 'system':
                            routes.system?.(data);
                            break;
                        default:
                            routes.default?.(message);
                    }
                } catch (e) {
                    console.error('消息路由错误:', e.message);
                }
            }
        })
    );

    return routingHold.writable.getWriter();
}

// ========== 示例4: 数据验证和转换链 ==========
export function createValidationChain(schema) {
    const validationHold = new TransformStream({
        transform(chunk, controller) {
            controller.enqueue(chunk);
        }
    });

    let validCount = 0;
    let invalidCount = 0;

    validationHold.readable.pipeTo(
        new WritableStream({
            async write(chunk) {
                try {
                    const data = JSON.parse(new TextDecoder().decode(chunk));
                    
                    // 简单验证逻辑
                    const isValid = schema.required.every(field => 
                        data.hasOwnProperty(field) && data[field] !== null
                    );

                    if (isValid) {
                        validCount++;
                        // 数据转换
                        const transformed = {
                            ...data,
                            validated: true,
                            processedAt: Date.now(),
                            id: `valid_${validCount}`
                        };
                        console.log('✅ 数据验证通过:', transformed);
                    } else {
                        invalidCount++;
                        console.log('❌ 数据验证失败:', {
                            data,
                            missing: schema.required.filter(field => !data.hasOwnProperty(field)),
                            id: `invalid_${invalidCount}`
                        });
                    }

                    // 统计信息
                    if ((validCount + invalidCount) % 10 === 0) {
                        console.log(`📊 验证统计: 有效=${validCount}, 无效=${invalidCount}`);
                    }
                } catch (e) {
                    console.error('验证处理错误:', e.message);
                }
            }
        })
    );

    return validationHold.writable.getWriter();
}

// ========== 示例5: 文件上传进度链 ==========
export function createUploadProgressChain(totalSize) {
    const progressHold = new TransformStream({
        transform(chunk, controller) {
            controller.enqueue(chunk);
        }
    });

    let uploadedSize = 0;
    let chunkCount = 0;

    progressHold.readable.pipeTo(
        new WritableStream({
            async write(chunk) {
                uploadedSize += chunk.byteLength;
                chunkCount++;
                
                const progress = Math.round((uploadedSize / totalSize) * 100);
                const speed = uploadedSize / (Date.now() - startTime) * 1000; // bytes/sec
                
                console.log(`📤 上传进度: ${progress}% (${uploadedSize}/${totalSize} bytes)`);
                console.log(`⚡ 上传速度: ${(speed / 1024).toFixed(2)} KB/s`);
                console.log(`📦 数据块: #${chunkCount} (${chunk.byteLength} bytes)`);

                // 模拟上传到服务器
                await new Promise(resolve => setTimeout(resolve, 10));

                if (uploadedSize >= totalSize) {
                    console.log('🎉 上传完成!');
                }
            }
        })
    );

    const startTime = Date.now();
    return progressHold.writable.getWriter();
}

// ========== 使用示例 ==========
export function runExamples() {
    console.log('🚀 开始TransformStream链式应用示例...\n');

    // 示例1: 压缩传输
    const compressor = createCompressionChain();
    compressor.write(new TextEncoder().encode('Hello World!'));
    compressor.write(new TextEncoder().encode('TransformStream is awesome!'));

    // 示例2: 日志处理
    const logger = createLogProcessingChain();
    logger.write(new TextEncoder().encode('[INFO] 系统启动'));
    logger.write(new TextEncoder().encode('[ERROR] 数据库连接失败'));
    logger.write(new TextEncoder().encode('[WARN] 内存使用率过高'));

    // 示例3: 消息路由
    const router = createMessageRoutingChain({
        broadcast: (data) => console.log('广播:', data),
        private: (target, data) => console.log(`私信给${target}:`, data),
        system: (data) => console.log('系统消息:', data),
        default: (msg) => console.log('未知消息:', msg)
    });
    
    router.write(new TextEncoder().encode(JSON.stringify({
        type: 'broadcast',
        data: '大家好！'
    })));

    // 示例4: 数据验证
    const validator = createValidationChain({
        required: ['name', 'email', 'age']
    });
    
    validator.write(new TextEncoder().encode(JSON.stringify({
        name: 'John',
        email: '<EMAIL>',
        age: 25
    })));

    // 示例5: 上传进度
    const uploader = createUploadProgressChain(1024);
    const chunk1 = new Uint8Array(512);
    const chunk2 = new Uint8Array(512);
    uploader.write(chunk1);
    uploader.write(chunk2);
}

// 导出所有示例
export const examples = {
    compression: createCompressionChain,
    logging: createLogProcessingChain,
    routing: createMessageRoutingChain,
    validation: createValidationChain,
    upload: createUploadProgressChain,
    run: runExamples
};
